<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Login Logs Controller
 * 
 * Admin controller for viewing and managing staff login logs
 * 
 * @package    CodeIgniter
 * @subpackage Controllers
 * @category   Admin
 * <AUTHOR> Name
 * @version    1.0
 */
class Staff_login_logs extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Check if user is logged in and has admin access
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        
        // Load required libraries and models
        $this->load->library('staff_login_logger');
        $this->load->model('staff_login_model');
        $this->load->helper('staff_login_helper');
    }

    /**
     * Index page - show login logs dashboard
     */
    public function index() {
        $data['title'] = 'Staff Login Logs';
        $data['stats'] = $this->staff_login_model->get_dashboard_stats(7);
        $data['active_sessions'] = $this->staff_login_model->get_active_sessions();
        $data['suspicious_logins'] = $this->staff_login_model->get_suspicious_logins(7);

        // Get today's login details
        $data['today_login_details'] = $this->staff_login_model->get_today_login_details();

        // Get users with multiple system logins
        $data['multiple_system_users'] = $this->staff_login_model->get_multiple_system_logins();

        // Get recent login logs
        $filters = array();
        $data['logs'] = $this->staff_login_model->get_login_logs($filters, 20, 0);
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/dashboard_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/dashboard_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/dashboard_new';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * View all login logs with filtering and pagination
     */
    public function logs() {
        $data['title'] = 'Staff Login Logs';
        
        // Get filters from GET parameters
        $filters = array(
            'user_id' => $this->input->get('user_id'),
            'username' => $this->input->get('username'),
            'ip_address' => $this->input->get('ip_address'),
            'date_from' => $this->input->get('date_from'),
            'date_to' => $this->input->get('date_to'),
            'login_method' => $this->input->get('login_method'),
            'device_type' => $this->input->get('device_type'),
            'is_active' => $this->input->get('is_active')
        );
        
        // Clean up empty filters
        foreach ($filters as $key => $value) {
            if ($value === null || $value === '') {
                unset($filters[$key]);
            }
        }
        
        // Pagination
        $limit = $this->input->get('limit') ? $this->input->get('limit') : 50;
        $offset = $this->input->get('offset') ? $this->input->get('offset') : 0;
        
        $data['logs'] = $this->staff_login_model->get_login_logs($filters, $limit, $offset);
        $data['filters'] = $filters;
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/logs_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/logs_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/logs_index';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * View active sessions
     */
    public function active_sessions() {
        $data['title'] = 'Active Staff Sessions';
        $data['active_sessions'] = $this->staff_login_model->get_active_sessions();
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/active_sessions_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/active_sessions_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/active_sessions';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * View user login history
     * 
     * @param int $user_id User ID
     */
    public function user_history($user_id = null) {
        if (!$user_id) {
            redirect('admin/staff_login_logs', 'refresh');
        }
        
        $data['title'] = 'User Login History';
        $data['user_id'] = $user_id;
        
        // Get user details
        $this->load->model('ion_auth_model');
        $data['user'] = $this->ion_auth_model->user($user_id)->row();
        
        if (!$data['user']) {
            $this->session->set_flashdata('flashError', 'User not found');
            redirect('admin/staff_login_logs', 'refresh');
        }
        
        $data['history'] = $this->staff_login_model->get_user_history($user_id, 100);
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/user_history_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/user_history_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/user_history_new';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * Force logout a user's active sessions
     * 
     * @param int $user_id User ID
     */
    public function force_logout($user_id = null) {
        if (!$user_id) {
            $this->session->set_flashdata('flashError', 'User ID is required');
            redirect('admin/staff_login_logs/active_sessions', 'refresh');
        }
        
        $result = $this->staff_login_model->force_logout_user($user_id, 'admin_forced');
        
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'User sessions have been terminated');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to terminate user sessions');
        }
        
        redirect('admin/staff_login_logs/active_sessions', 'refresh');
    }

    /**
     * Export login logs to CSV
     */
    public function export_csv() {
        // Get filters from GET parameters
        $filters = array(
            'user_id' => $this->input->get('user_id'),
            'username' => $this->input->get('username'),
            'ip_address' => $this->input->get('ip_address'),
            'date_from' => $this->input->get('date_from'),
            'date_to' => $this->input->get('date_to'),
            'login_method' => $this->input->get('login_method'),
            'device_type' => $this->input->get('device_type'),
            'is_active' => $this->input->get('is_active')
        );
        
        // Clean up empty filters
        foreach ($filters as $key => $value) {
            if ($value === null || $value === '') {
                unset($filters[$key]);
            }
        }
        
        $csv_content = $this->staff_login_model->export_to_csv($filters);
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="staff_login_logs_' . date('Y-m-d') . '.csv"');
        
        echo $csv_content;
        exit;
    }

    /**
     * Configuration settings
     */
    public function config() {
        $data['title'] = 'Login Logging Configuration';
        
        if ($this->input->post()) {
            // Update configuration
            $config_keys = array(
                'log_retention_days',
                'track_ip_location',
                'log_user_agent_details',
                'auto_close_inactive_sessions',
                'session_timeout_minutes',
                'enable_concurrent_session_limit',
                'max_concurrent_sessions'
            );
            
            foreach ($config_keys as $key) {
                if ($this->input->post($key) !== null) {
                    $this->staff_login_model->set_config($key, $this->input->post($key));
                }
            }
            
            $this->session->set_flashdata('flashSuccess', 'Configuration updated successfully');
            redirect('admin/staff_login_logs/config', 'refresh');
        }
        
        // Get current configuration
        $config_keys = array(
            'log_retention_days',
            'track_ip_location',
            'log_user_agent_details',
            'auto_close_inactive_sessions',
            'session_timeout_minutes',
            'enable_concurrent_session_limit',
            'max_concurrent_sessions'
        );
        
        $data['config'] = array();
        foreach ($config_keys as $key) {
            $data['config'][$key] = $this->staff_login_model->get_config($key);
        }
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/config_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/config_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/config';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * Clean up old logs
     */
    public function cleanup() {
        $days = $this->input->get('days') ? $this->input->get('days') : 90;
        
        $deleted_count = $this->staff_login_model->cleanup_old_logs($days);
        
        $this->session->set_flashdata('flashSuccess', "Cleaned up {$deleted_count} old login logs");
        redirect('admin/staff_login_logs', 'refresh');
    }

    /**
     * Test the login logging system
     * This is for development/testing only
     */
    public function test() {
        // Only allow in development environment
        if (ENVIRONMENT !== 'development') {
            show_error('This function is only available in development environment');
        }
        
        $output = '';
        
        // Test 1: Log a manual login
        $user_id = $this->session->userdata('user_id');
        $username = $this->session->userdata('username');
        
        if (!$user_id || !$username) {
            $output .= "Test failed: You must be logged in to run this test\n";
        } else {
            // Log a test login
            $this->load->library('staff_login_logger');
            $login_id = $this->staff_login_logger->log_login($user_id, $username, 'test');
            
            if ($login_id) {
                $output .= "Test 1 passed: Successfully logged a test login with ID {$login_id}\n";
                
                // Test 2: Track session IP
                $result = $this->staff_login_logger->track_session_ip();
                $output .= "Test 2 " . ($result ? "passed" : "failed") . ": Session IP tracking\n";
                
                // Test 3: Log a logout
                $result = $this->staff_login_logger->log_logout($user_id, 'test');
                $output .= "Test 3 " . ($result ? "passed" : "failed") . ": Logout logging\n";
            } else {
                $output .= "Test 1 failed: Could not log a test login\n";
            }
        }
        
        // Test 4: Get login statistics
        $stats = $this->staff_login_model->get_dashboard_stats(7);
        $output .= "Test 4 " . ($stats ? "passed" : "failed") . ": Get dashboard statistics\n";
        
        // Test 5: Get active sessions
        $active_sessions = $this->staff_login_model->get_active_sessions();
        $output .= "Test 5 passed: Found " . count($active_sessions) . " active sessions\n";
        
        // Display test results
        $data['title'] = 'Login Logging System Test';
        $data['output'] = $output;
        $data['user_id'] = $user_id;
        $data['username'] = $username;
        $data['ip_address'] = $this->input->ip_address();
        $data['user_agent'] = $this->input->user_agent();
        $data['session_id'] = session_id();
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/test_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/test_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/test';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * Initialize tracking for already logged-in staff users
     * This method can be called to create initial tracking records for existing sessions
     */
    public function initialize_tracking() {
        // Only allow in development environment or for admins
        if (ENVIRONMENT !== 'development' && !$this->ion_auth->is_admin()) {
            show_error('This function is only available in development environment or for administrators');
        }

        $this->load->library('staff_login_logger');
        $output = '';
        $initialized_count = 0;

        // Get all currently active sessions from ci_sessions table
        $active_sessions = $this->db->select('user_id, session_id')
                                   ->where('user_id IS NOT NULL')
                                   ->where('user_id >', 0)
                                   ->get('ci_sessions')
                                   ->result();

        foreach ($active_sessions as $session) {
            // Check if user is a staff member
            $avatar = $this->db->select('avatar_type, stakeholder_id')
                              ->where('user_id', $session->user_id)
                              ->where('avatar_type', 4)
                              ->get('avatar')
                              ->row();

            if ($avatar) {
                // Get user details
                $user = $this->db->select('username')
                                ->where('id', $session->user_id)
                                ->get('users')
                                ->row();

                if ($user) {
                    // Check if already has a tracking record
                    $existing_log = $this->db->select('id')
                                            ->where('user_id', $session->user_id)
                                            ->where('session_id', $session->session_id)
                                            ->where('is_active', 1)
                                            ->get('staff_login_logs')
                                            ->row();

                    if (!$existing_log) {
                        // Create initial tracking record
                        $login_log_id = $this->staff_login_logger->create_initial_session_record(
                            $session->user_id,
                            $user->username
                        );

                        if ($login_log_id) {
                            $initialized_count++;
                            $output .= "Initialized tracking for User ID {$session->user_id} ({$user->username}) - Log ID: {$login_log_id}\n";
                        } else {
                            $output .= "Failed to initialize tracking for User ID {$session->user_id} ({$user->username})\n";
                        }
                    } else {
                        $output .= "User ID {$session->user_id} ({$user->username}) already has tracking record\n";
                    }
                }
            }
        }

        $output .= "\nSummary: Initialized tracking for {$initialized_count} staff members\n";

        // Display results
        $data['title'] = 'Initialize Staff Login Tracking';
        $data['output'] = $output;
        $data['initialized_count'] = $initialized_count;
        $data['total_sessions'] = count($active_sessions);

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/initialize_result_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/initialize_result_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/initialize_result';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * Debug method to check system status
     */
    public function debug() {
        // Only allow in development environment
        if (ENVIRONMENT !== 'development') {
            show_error('This function is only available in development environment');
        }

        $debug_info = array();

        // Check if hooks are enabled
        $debug_info['hooks_enabled'] = config_item('enable_hooks');

        // Check if staff login logging is enabled
        $this->load->helper('staff_login_helper');
        $debug_info['staff_logging_enabled'] = get_staff_login_setting('staff_login_logging_enabled', '1');
        $debug_info['log_files_enabled'] = get_staff_login_setting('staff_login_log_files_enabled', '0');

        // Check current user info
        if ($this->ion_auth->logged_in()) {
            $user = $this->ion_auth->user()->row();
            $debug_info['current_user'] = array(
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email
            );

            // Check if current user is staff
            $avatar = $this->db->select('avatar_type, stakeholder_id')
                              ->where('user_id', $user->id)
                              ->get('avatar')
                              ->row();

            $debug_info['current_user']['avatar'] = $avatar ? array(
                'avatar_type' => $avatar->avatar_type,
                'is_staff' => $avatar->avatar_type == 4,
                'stakeholder_id' => $avatar->stakeholder_id
            ) : null;

            // Check session data
            $debug_info['session_data'] = array(
                'session_id' => session_id(),
                'staff_login_log_id' => $this->session->userdata('staff_login_log_id'),
                'user_id' => $this->session->userdata('user_id'),
                'last_ip_track_time' => $this->session->userdata('last_ip_track_time')
            );

            // Check if there's an active log record
            $login_log_id = $this->session->userdata('staff_login_log_id');
            if ($login_log_id) {
                $log_record = $this->db->where('id', $login_log_id)
                                      ->get('staff_login_logs')
                                      ->row();
                $debug_info['current_log_record'] = $log_record ? array(
                    'id' => $log_record->id,
                    'login_time' => $log_record->login_time,
                    'ip_address' => $log_record->ip_address,
                    'is_active' => $log_record->is_active
                ) : 'Record not found';
            }
        }

        // Check database tables
        $debug_info['tables_exist'] = array(
            'staff_login_logs' => $this->db->table_exists('staff_login_logs'),
            'staff_login_config' => $this->db->table_exists('staff_login_config'),
            'avatar' => $this->db->table_exists('avatar'),
            'users' => $this->db->table_exists('users')
        );

        // Check recent logs
        if ($this->db->table_exists('staff_login_logs')) {
            $recent_logs = $this->db->select('id, user_id, username, login_time, ip_address, is_active')
                                   ->order_by('login_time', 'DESC')
                                   ->limit(5)
                                   ->get('staff_login_logs')
                                   ->result_array();
            $debug_info['recent_logs'] = $recent_logs;
        }

        // Display debug information
        $data['title'] = 'Staff Login Logging Debug';
        $data['debug_info'] = $debug_info;

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/debug_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/debug_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/debug';
        }
        $this->load->view('inc/template', $data);
    }

    /**
     * View today's login details
     */
    public function today_logins() {
        $data['title'] = 'Today\'s Login Details';
        $data['today_login_details'] = $this->staff_login_model->get_today_login_details();

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/today_logins_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/today_logins_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/today_logins';
        }

        $this->load->view('admin/layout', $data);
    }

    /**
     * View multiple system login details
     */
    public function multiple_system_logins() {
        $data['title'] = 'Multiple System Logins';
        $data['multiple_system_users'] = $this->staff_login_model->get_multiple_system_logins();

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/multiple_system_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/multiple_system_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/multiple_system_logins';
        }

        $this->load->view('admin/layout', $data);
    }

    /**
     * Get login logs with date range filter
     */
    public function date_range_report() {
        $data['title'] = 'Login Report by Date Range';

        // Get filter parameters
        $date_from = $this->input->get('date_from');
        $date_to = $this->input->get('date_to');
        $filters = array(
            'username' => $this->input->get('username'),
            'ip_address' => $this->input->get('ip_address'),
            'device_type' => $this->input->get('device_type'),
            'browser_name' => $this->input->get('browser_name'),
            'is_active' => $this->input->get('is_active')
        );

        // Set default date range if not provided
        if (!$date_from) {
            $date_from = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$date_to) {
            $date_to = date('Y-m-d');
        }

        // Get paginated results
        $page = (int)$this->input->get('page') ?: 1;
        $per_page = 50;
        $offset = ($page - 1) * $per_page;

        $data['logs'] = $this->staff_login_model->get_logs_by_date_range($date_from, $date_to, $filters, $per_page, $offset);
        $data['filters'] = array_merge($filters, array('date_from' => $date_from, 'date_to' => $date_to));

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'admin/staff_login_logs/date_range_report_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'admin/staff_login_logs/date_range_report_mobile';
        } else {
            $data['main_content'] = 'admin/staff_login_logs/date_range_report';
        }

        $this->load->view('admin/layout', $data);
    }

    /**
     * Debug method to test login logging functionality
     * Remove this method in production
     */
    public function debug_test() {
        if (!$this->ion_auth->is_admin()) {
            show_404();
            return;
        }

        echo "<h2>Staff Login Logging Debug Test</h2>";

        // Test database connection
        echo "<h3>1. Database Connection Test</h3>";
        if ($this->db->table_exists('staff_login_logs')) {
            echo "✅ staff_login_logs table exists<br>";

            // Count total records
            $total_records = $this->db->count_all('staff_login_logs');
            echo "📊 Total records in staff_login_logs: {$total_records}<br>";

            // Count active sessions
            $active_sessions = $this->db->where('is_active', 1)->count_all_results('staff_login_logs');
            echo "🟢 Active sessions: {$active_sessions}<br>";

            // Count today's logins
            $today_logins = $this->db->where('DATE(login_time)', date('Y-m-d'))->count_all_results('staff_login_logs');
            echo "📅 Today's logins: {$today_logins}<br>";
        } else {
            echo "❌ staff_login_logs table does not exist<br>";
        }

        // Test configuration
        echo "<h3>2. Configuration Test</h3>";
        if ($this->db->table_exists('staff_login_config')) {
            echo "✅ staff_login_config table exists<br>";

            $configs = $this->db->get('staff_login_config')->result();
            foreach ($configs as $config) {
                echo "⚙️ {$config->config_key} = {$config->config_value}<br>";
            }
        } else {
            echo "❌ staff_login_config table does not exist<br>";
        }

        // Test current user session
        echo "<h3>3. Current Session Test</h3>";
        if ($this->ion_auth->logged_in()) {
            $user = $this->ion_auth->user()->row();
            echo "👤 Current user: {$user->username} (ID: {$user->id})<br>";
            echo "🔑 Session ID: " . session_id() . "<br>";

            $login_log_id = $this->session->userdata('staff_login_log_id');
            echo "📝 Login log ID in session: " . ($login_log_id ?: 'Not set') . "<br>";

            // Check if user is staff
            $avatar = $this->db->select('avatar_type, stakeholder_id')
                              ->where('user_id', $user->id)
                              ->where('avatar_type', 4)
                              ->get('avatar')
                              ->row();

            if ($avatar) {
                echo "✅ User is a staff member (stakeholder_id: {$avatar->stakeholder_id})<br>";
            } else {
                echo "❌ User is not a staff member<br>";
            }

            // Check current session in logs
            $current_session = $this->db->select('id, login_time, ip_address, is_active')
                                       ->where('user_id', $user->id)
                                       ->where('session_id', session_id())
                                       ->get('staff_login_logs')
                                       ->row();

            if ($current_session) {
                echo "✅ Current session found in logs (ID: {$current_session->id})<br>";
                echo "🕐 Login time: {$current_session->login_time}<br>";
                echo "🌐 IP address: {$current_session->ip_address}<br>";
                echo "🔄 Is active: " . ($current_session->is_active ? 'Yes' : 'No') . "<br>";
            } else {
                echo "❌ Current session not found in logs<br>";
            }
        } else {
            echo "❌ User not logged in<br>";
        }

        // Test recent logs
        echo "<h3>4. Recent Login Logs</h3>";
        $recent_logs = $this->db->select('sll.*, u.username')
                               ->from('staff_login_logs sll')
                               ->join('users u', 'sll.user_id = u.id', 'left')
                               ->order_by('sll.login_time', 'DESC')
                               ->limit(5)
                               ->get()
                               ->result();

        if ($recent_logs) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Username</th><th>IP</th><th>Login Time</th><th>Device</th><th>Browser</th><th>Active</th></tr>";
            foreach ($recent_logs as $log) {
                echo "<tr>";
                echo "<td>{$log->id}</td>";
                echo "<td>{$log->username}</td>";
                echo "<td>{$log->ip_address}</td>";
                echo "<td>{$log->login_time}</td>";
                echo "<td>{$log->device_type}</td>";
                echo "<td>{$log->browser_name}</td>";
                echo "<td>" . ($log->is_active ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "No recent logs found<br>";
        }

        echo "<br><strong>Note:</strong> Remove this debug method in production!";
    }
}
